<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="theme-color" content="#6777ef"/>
    <link rel="apple-touch-icon" href="{{ asset('frontend/dist/images/logo/logo.png') }}">
    <link rel="manifest" href="{{ asset('/manifest.json') }}">
    <title>Phần mềm kỹ năng sống Novastars</title>
    <link rel="stylesheet" href="{{asset('frontend/dist/main.css')}}" />
    <link rel="icon" type="image/png" href="{{asset('frontend/dist/images/favicon/favicon.png')}}" />
    <link rel="stylesheet" href="{{asset('frontend/fontawesome-free-5.15.4-web/css/all.min.css')}}">
    <style>
        .dropdown {
            min-width: 400px;

        }

        .btn.dropdown-toggle {
            background-color: white;
            color: #333;
            border-radius: 5px;
            padding: 10px 15px;
            border: 1px solid #ccc;
            text-align: left;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            position: relative;
            font-weight: normal;
        }

        .btn.dropdown-toggle::after {
            content: '\25BC';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 30px;
            background-color: #d8712f;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
        }

        .dropdown-menu {
            min-width: 400px;
            border-radius: 5px;
            border: 1px solid #ccc;
            padding: 0;
            max-height: 400px;
            overflow-y: auto;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #eee;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item i {
            margin-right: 10px;
            color: #007bff;
            font-size: 16px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-header {
            font-weight: bold;
            padding: 12px 15px;
            background-color: #f1f1f1;
            border-bottom: 1px solid #e3e3e3;
            color: #333;
            font-size: 16px;
        }

        .coursedescription-header-center {
            display: flex;
            align-items: center;
        }
        
        /* Add custom styles for progress bar and buttons */
        #progress-bar {
            display: none;
            width: 100%;
        }
        #download-message {
            color: green;
            font-weight: bold;
        }
        #action-buttons {
            margin-top: 20px;
        }
    </style>
<!--<script>
        let selectedGameId = null;
        let selectedGameVersion = null;

        // Main function to load game with safe update strategy
        function loadGameWithUpdate(gameId) {
            // Step 1: Load the cached version first, if it exists
            loadCachedGame(gameId);

            // Step 2: Try to download the new version and store it in temporary storage
            fetch(`/storage/webgl/${gameId}/index.html`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.blob();
                })
                .then(newGameData => {
                    // Step 3: Store the new game temporarily
                    storeInTemporaryStorage(gameId, newGameData);

                    // Step 4: Once download is complete, replace the old cached version
                    replaceCachedGameWithNewVersion(gameId, newGameData);
                })
                .catch(error => {
                    console.error('Error fetching new game version, keeping old version:', error);
                    // Fallback to old version (nothing changes, as old version is still in IndexedDB)
                });
        }

        // Function to load cached game
        function loadCachedGame(gameId) {
            const dbRequest = indexedDB.open('webgl-games', 1);

            dbRequest.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction('games', 'readonly');
                const store = transaction.objectStore('games');

                store.get(gameId).onsuccess = function(event) {
                    const cachedGame = event.target.result;
                    if (cachedGame) {
                        console.log('Loaded from cache');
                        document.getElementById('game-iframe').src = URL.createObjectURL(cachedGame.gameData);
                    } else {
                        console.log('No cached game found, need to download');
                    }
                };
            };
        }

        // Function to store game in temporary storage
        function storeInTemporaryStorage(gameId, gameData) {
            const tempDbRequest = indexedDB.open('temp-webgl-games', 1);

            tempDbRequest.onupgradeneeded = function(event) {
                const db = event.target.result;
                const store = db.createObjectStore('tempGames', { keyPath: 'id' });
            };

            tempDbRequest.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction('tempGames', 'readwrite');
                const store = transaction.objectStore('tempGames');

                store.put({ id: gameId, gameData: gameData });
                console.log(`Stored new game in temporary storage for game ${gameId}`);
            };

            tempDbRequest.onerror = function() {
                console.error("Error opening temp IndexedDB.");
            };
        }

        // Function to replace cached game with new version
        function replaceCachedGameWithNewVersion(gameId, newGameData) {
            const dbRequest = indexedDB.open('webgl-games', 1);

            dbRequest.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction('games', 'readwrite');
                const store = transaction.objectStore('games');

                store.put({ id: gameId, gameData: newGameData });
                console.log(`Replaced old cached version with the new version for game ${gameId}`);
            };

            dbRequest.onerror = function() {
                console.error("Error opening IndexedDB for replacement.");
            };
        }
    </script> -->
</head>

<body style="background-color: #ebebf2;">

    <!-- Title Starts Here -->
    <header class="bg-transparent">
        <div class="container-fluid">
            <div class="coursedescription-header">
                <div class="coursedescription-header-start">
                    <a class="logo-image" href="{{route('home')}}">
                        <img src="{{asset('frontend/dist/images/logo/logo.png')}}" alt="Logo" class="img-fluid"
                            width="133" height="29" />
                    </a>
                    <div class="topic-info mt-1 mb-1">
                        <div class="topic-info-arrow">
                            <a href="{{ route('home') }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </div>
                        <div class="topic-info-text">
                            <h6 class="font-title--card"><a href="#">{{$course->title_en}}</a></h6>
                        </div>
                    </div>
                </div>
                <div class="coursedescription-header-center d-flex align-items-center">
                    <strong class="me-3">Chọn bài</strong>
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            Bài học
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            @foreach($lessons as $lesson)
                                <li>
                                    <h6 class="dropdown-header">{{$loop->iteration}}. {{$lesson->title}}</h6>
                                    @foreach ($lesson->material->whereNull('deleted_at')->sortBy('content') as $material)
                                        @if(in_array($material->content, $materialArr))
                                            <a class="dropdown-item main-wizard" href="#" data-material-title="{{$material->title}}"
                                                data-lesson-description="{{$lesson->description}}"
                                                data-lesson-notes="{{$lesson->notes}}" data-content-url="{{$material->content_url}}"
                                                data-document-url="{{$material->document_url}}" data-type="{{$material->type}}">
                                                @if ($material->type == 'video')
                                                    <i class="far fa-play-circle fa-lg"></i>
                                                @else
                                                    <i class="far fa-file fa-lg text-success"></i>
                                                @endif
                                                {{$material->title}}
                                            </a>
                                        @endif
                                    @endforeach
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>


                <div class="coursedescription-header-end mb-0 d-flex gap-2">
                    <a href="" id="courseFullScreen" target="_blank"><button class="button" style="background-color: #D8712F;">Giáo án</button></a>
                    <button class="button" style="background-color: #D8712F;" onclick="reloadWithoutCache()">Làm mới</button>
                </div>
            </div>
        </div>
    </header>
    <!-- Ttile Ends Here -->

    <!-- Course Description Starts Here -->
    <div class=" container-fluid">
        <div class="row course-description">

            {{-- Video Area --}}
            <div class="col-lg-12">
                <div class="course-description-start">
                    <div class="video-area">
                        <iframe src="" height="680px" width="100%" id="courseIframeScreen" allowfullscreen="true"
                            scrolling="no" title="Iframe Example" style="margin: 0; padding: 0">

                        </iframe>
                    </div>
                    <!-- <div class="course-description-start-content"> -->
                        <!-- <h5 class="font-title--sm material-title">{{$course->title_en}}</h5> -->
                        <!-- <nav class="course-description-start-content-tab">
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button class="nav-link active" id="nav-ldescrip-tab" data-bs-toggle="tab"
                                    data-bs-target="#nav-ldescrip" type="button" role="tab" aria-controls="nav-ldescrip"
                                    aria-selected="true">
                                    Mô tả bài học
                                </button>
                                <button class="nav-link" id="nav-lnotes-tab" data-bs-toggle="tab"
                                    data-bs-target="#nav-lnotes" type="button" role="tab" aria-controls="nav-lnotes"
                                    aria-selected="false">Ghi chú</button>
                            </div>
                        </nav> -->
                        <div class="tab-content course-description-start-content-tabitem" id="nav-tabContent">
                            <!-- Lesson Description Starts Here -->
                            <!-- <div class="tab-pane fade show active" id="nav-ldescrip" role="tabpanel"
                                aria-labelledby="nav-ldescrip-tab">
                                <div class="lesson-description">
                                    <p>
                                    </p>
                                </div>
                            </div> -->
                            <!-- Course Notes Starts Here -->
                            <div class="tab-pane fade" id="nav-lnotes" role="tabpanel" aria-labelledby="nav-lnotes-tab">
                                <div class="course-notes-area">
                                    <div class="course-notes">
                                        <div class="course-notes-item">
                                            <p>
                                                <!-- Course Notes here - execution with Javascript -->
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <!-- Course Notes Ends Here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Index Course Contents --}}
            <!-- <div class="col-lg-3">
                <div class="videolist-area">
                    <div class="videolist-area-heading">
                        <h6>Nội dung bài học</h6>
                    </div>
                    <div class="videolist-area-bar__wrapper">
                        @foreach($lessons as $lesson)
                            <div class="videolist-area-wizard" data-lesson-description="{{$lesson->description}}"
                                data-lesson-notes="{{$lesson->notes}}">
                                <div class="wizard-heading">
                                    <h6 class="">{{$loop->iteration}}. {{$lesson->title}}</h6>
                                </div>
                                @foreach ($lesson->material as $material)
                                    @if(in_array($material->content, $materialArr))
                                        <div class="main-wizard"
                                             data-material-title="{{$loop->parent->iteration}}.{{$loop->iteration}} {{$material->title}}">
                                            <input type="hidden" value="{{ $material->content_url }}" id="content-url">
                                            <div class="main-wizard__wrapper">
                                                <a class="main-wizard-start" onclick="show_video('{{$material->content}}')">
                                                    @if ($material->type=='video')
                                                        <div class="main-wizard-icon">
                                                            <i class="far fa-play-circle fa-lg"></i>
                                                        </div>
                                                    @else
                                                        <div class="main-wizard-icon">
                                                            <i class="far fa-file fa-lg text-success"></i>
                                                        </div>
                                                    @endif
                                                    <div class="main-wizard-title">
                                                        <p>{{$loop->parent->iteration}}.{{$loop->iteration}} {{$material->title}}
                                                        </p>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endforeach
                    </div>
                </div>
            </div> -->
        </div>
    </div>
    <!-- Course Description Ends Here -->

    <script src="{{asset('frontend/src/js/jquery.min.js')}}"></script>
    <script src="{{asset('frontend/src/js/bootstrap.bundle.min.js')}}"></script>
    <script src="{{asset('frontend/src/js/app.js')}}"></script>
    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>

    <!-- Star Rating
    <script>
        $(".my-rating").starRating({
            starSize: 30,
            activeColor: "#FF7A1A",
            hoverColor: "#FF7A1A",
            ratedColors: ["#FF7A1A", "#FF7A1A", "#FF7A1A", "#FF7A1A", "#FF7A1A"],
            starShape: "rounded",
        });
    </script> -->

    <script>
        // Track offline status
        let wasOffline = !navigator.onLine;

        function reloadWithoutCache() {
            // Get base URL without query parameters
            let baseUrl = window.location.href.split('?')[0];
            
            // Add cache-busting parameter to URL
            let nocacheUrl = baseUrl + '?nocache=' + new Date().getTime();
            
            // Force reload bypassing cache
            window.location.replace(nocacheUrl);
        }

        // Automatic refresh on reconnection
        window.addEventListener('online', function() {
            if (wasOffline) {
                console.log('Internet connection restored, reloading without cache...');
                reloadWithoutCache();
            }
        });

        window.addEventListener('offline', function() {
            wasOffline = true;
            console.log('Gone offline');
        });

        $(document).ready(function () {
            $('.dropdown-item.main-wizard').on('click', function (e) {
                e.preventDefault();
                var lessonDescription = $(this).data('lesson-description');
                var lessonNotes = $(this).data('lesson-notes');
                var materialTitle = $(this).data('material-title');
                var contentUrl = $(this).data('content-url');
                var documentUrl = $(this).data('document-url');
                var type = $(this).data('type');

                $('#nav-ldescrip .lesson-description p').html(lessonDescription);
                $('#nav-lnotes .course-notes-area .course-notes-item p').html(lessonNotes);
                $('.material-title').html(materialTitle);

                $('#courseFullScreen').attr('href', documentUrl);

                if (type === 'video') {
                    var finalUrl;

                    // Check if contentUrl already contains a full URL or starts with /
                    if (contentUrl.startsWith('http') || contentUrl.startsWith('/')) {
                        // contentUrl is already a full URL or absolute path
                        finalUrl = contentUrl;
                    } else {
                        // contentUrl is a relative path, construct the webgl URL
                        // Handle spaces and special characters in the URL
                        var encodedContentUrl = encodeURIComponent(contentUrl);
                        finalUrl = '{{ env('APP_URL') . '/webgl' }}' + '/' + encodedContentUrl + '/index.html';
                    }

                    console.log('Original contentUrl:', contentUrl);
                    console.log('Final iframe src:', finalUrl);
                    $('#courseIframeScreen').attr('src', finalUrl);
                } else {
                    $('#courseIframeScreen').attr('src', contentUrl);
                }

                $('#dropdownMenuButton').text(materialTitle);
            });
        });

    </script>

    
</body>

</html>
