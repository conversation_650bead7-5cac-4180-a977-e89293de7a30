<?php

use App\Http\Controllers\AssetController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ClassLevelController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\WatchCourseController as watchCourse;
use Illuminate\Support\Facades\Route;

/* students */
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


/* students controllers */
Route::get('/register', [AuthController::class, 'register'])->name('register');
Route::post('/register', [AuthController::class, 'registerStore'])->name('register.store');
Route::get('/login', [AuthController::class, 'signInForm'])->name('login');
Route::post('/login/{back_route}', [AuthController::class, 'loginCheck'])->name('login.check');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
    Route::post('/profile/save', [ProfileController::class, 'save_profile'])->name('save_profile');
    Route::post('/profile/change-password', [ProfileController::class, 'change_password'])->name('change_password');
    Route::post('/change-image', [ProfileController::class, 'changeImage'])->name('change_image');
});

// frontend pages
Route::middleware(['auth'])->group(function () {
    Route::get('home', [DashboardController::class, 'index'])->name('home');
    Route::get('/', [DashboardController::class, 'index'])->name('home');
    Route::get('watchCourse/{type}/{id}', [watchCourse::class, 'watchCourse'])->name('watchCourse');

    // Guide Page Route (Protected by checkauth middleware)
    Route::get('/huong-dan', function () {
        return view('huongdan');
    })->name('huongdan');
});

Route::group(['middleware' => ['auth'], 'prefix' => 'webgl'], function () {
    Route::get('/{classLevel}/{assetFolder}/{assetFile}', [AssetController::class, 'assetFile']);
    Route::get('/{classLevel}/{assetFolder}/{folderLevel1}/{folderLevel2}/{assetFile}', [AssetController::class, 'assetFile2']);
    Route::get('/{classLevel}/index.html', [ClassLevelController::class, 'index']);
});

// Additional routes to handle direct access without /webgl/ prefix
Route::group(['middleware' => ['auth']], function () {
    // Handle URLs like /Lop 3 - Chuong trinh He/index.html directly
    Route::get('/{classLevel}/index.html', [ClassLevelController::class, 'index'])
        ->where('classLevel', '.*'); // Allow any characters including spaces
});
