<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;

class ClassLevelController extends Controller
{
    /**
     * Map descriptive names to actual folder names
     */
    private function mapClassLevelToFolder($classLevel)
    {
        // URL decode the class level first (handle multiple encoding levels)
        $decodedClassLevel = urldecode(urldecode($classLevel));

        // Log the original and decoded values for debugging
        Log::info("Original classLevel: " . $classLevel);
        Log::info("Decoded classLevel: " . $decodedClassLevel);

        // Create mapping for common patterns
        $mappings = [
            // Grade 1 mappings
            'Lop 1' => 'lop-1-1',
            'Lớp 1' => 'lop-1-1',
            'lop-1' => 'lop-1-1',

            // Grade 2 mappings
            'Lop 2' => 'lop-2-1',
            'Lớp 2' => 'lop-2-1',
            'lop-2' => 'lop-2-1',

            // Grade 3 mappings
            'Lop 3' => 'lop-3-1',
            'Lớp 3' => 'lop-3-1',
            'lop-3' => 'lop-3-1',
            'Lop 3 - Chuong trinh He' => 'lop-3-1',
            'Lớp 3 - Chương trình Hè' => 'lop-3-1',
            'Lop3-ChuongtrinhHe' => 'lop-3-1',

            // Grade 4 mappings
            'Lop 4' => 'lop-4-1',
            'Lớp 4' => 'lop-4-1',
            'lop-4' => 'lop-4-1',

            // Grade 5 mappings
            'Lop 5' => 'lop-5-1',
            'Lớp 5' => 'lop-5-1',
            'lop-5' => 'lop-5-1',
        ];

        // Check if we have a direct mapping
        if (isset($mappings[$decodedClassLevel])) {
            Log::info("Found direct mapping: " . $decodedClassLevel . " -> " . $mappings[$decodedClassLevel]);
            return $mappings[$decodedClassLevel];
        }

        // Try case-insensitive matching
        foreach ($mappings as $key => $value) {
            if (strcasecmp($key, $decodedClassLevel) === 0) {
                Log::info("Found case-insensitive mapping: " . $decodedClassLevel . " -> " . $value);
                return $value;
            }
        }

        // If no direct mapping, try to find the best match
        return $this->findBestFolderMatch($decodedClassLevel);
    }

    /**
     * Find the best matching folder for a given class level
     */
    private function findBestFolderMatch($classLevel)
    {
        $webglPath = storage_path('app/public/webgl');

        if (!File::exists($webglPath)) {
            return $classLevel; // Return original if webgl directory doesn't exist
        }

        $folders = File::directories($webglPath);
        $folderNames = array_map('basename', $folders);

        // Extract grade number from the class level
        if (preg_match('/(\d+)/', $classLevel, $matches)) {
            $gradeNumber = $matches[1];

            // Look for folders that match the grade pattern
            $pattern = 'lop-' . $gradeNumber . '-';
            foreach ($folderNames as $folder) {
                if (strpos($folder, $pattern) === 0) {
                    Log::info("Found matching folder: " . $folder . " for grade: " . $gradeNumber);
                    return $folder;
                }
            }
        }

        // If no pattern match, return the original (might still work)
        return $classLevel;
    }

    public function index(Request $request, $classLevel)
    {
        // Map the class level to actual folder name
        $actualFolder = $this->mapClassLevelToFolder($classLevel);

        $path = storage_path('app/public/webgl/' . $actualFolder . '/' . 'index.html');

        Log::info("Trying to access file: " . $path);

        if (!File::exists($path)) {
            // Try alternative approaches if the mapped folder doesn't work
            $alternativePaths = $this->getAlternativePaths($classLevel);

            foreach ($alternativePaths as $altPath) {
                Log::info("Trying alternative path: " . $altPath);
                if (File::exists($altPath)) {
                    $path = $altPath;
                    break;
                }
            }

            // If still not found, return 404
            if (!File::exists($path)) {
                Log::error("File not found: " . $path);
                abort(404, 'WebGL content not found for: ' . $classLevel);
            }
        }

        $file = File::get($path);
        $type = File::mimeType($path);
        $response = Response::make($file, 200);
        $response->header("Content-Type", 'text/html');

        return $response;
    }

    /**
     * Get alternative paths to try if the main mapping fails
     */
    private function getAlternativePaths($classLevel)
    {
        $decodedClassLevel = urldecode($classLevel);
        $basePath = storage_path('app/public/webgl/');

        $alternatives = [
            // Try the original class level as-is
            $basePath . $classLevel . '/index.html',
            $basePath . $decodedClassLevel . '/index.html',

            // Try URL-encoded version
            $basePath . rawurlencode($classLevel) . '/index.html',

            // Try with spaces replaced by dashes
            $basePath . str_replace(' ', '-', $decodedClassLevel) . '/index.html',
            $basePath . str_replace(' ', '_', $decodedClassLevel) . '/index.html',

            // Try lowercase versions
            $basePath . strtolower(str_replace(' ', '-', $decodedClassLevel)) . '/index.html',
            $basePath . strtolower(str_replace(' ', '_', $decodedClassLevel)) . '/index.html',
        ];

        return $alternatives;
    }
}
